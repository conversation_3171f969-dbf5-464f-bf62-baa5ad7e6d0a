
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Protocol;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GGEC.AOI.T68ZJ.Client.Mqtt
{
    /// <summary>
    /// 简单的MQTT客户端管理器，支持订阅和发送
    /// </summary>
    public class SimpleMqttManager : IDisposable
    {
        #region 私有字段
        private IMqttClient _mqttClient;
        private bool _disposed = false;
        private readonly Dictionary<string, MqttQualityOfServiceLevel> _subscriptions = new Dictionary<string, MqttQualityOfServiceLevel>();
        #endregion

        #region 配置属性
        /// <summary>
        /// 服务器地址
        /// </summary>
        public string ServerEndpoint { get; set; } = "**************";

        /// <summary>
        /// 服务器端口
        /// </summary>
        public int ServerPort { get; set; } = 10086;

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; } = "simple_mqtt_client";

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = "admin";

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; } = "123456";

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _mqttClient?.IsConnected ?? false;
        #endregion

        #region 事件
        /// <summary>
        /// 消息接收事件
        /// </summary>
        public event Action<string, string> MessageReceived;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event Action<bool> ConnectionChanged;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event Action<string> ErrorOccurred;
        #endregion

        #region 构造函数
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SimpleMqttManager()
        {
            InitializeClient();
        }

        /// <summary>
        /// 带参数构造函数
        /// </summary>
        /// <param name="serverEndpoint">服务器地址</param>
        /// <param name="serverPort">服务器端口</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        public SimpleMqttManager(string serverEndpoint, int serverPort, string clientId, string username = "t68zjexe001", string password = "123456")
        {
            ServerEndpoint = serverEndpoint;
            ServerPort = serverPort;
            ClientId = clientId;
            Username = username;
            Password = password;
            InitializeClient();
        }
        #endregion

        #region 初始化
        /// <summary>
        /// 初始化MQTT客户端
        /// </summary>
        private void InitializeClient()
        {
            try
            {
                _mqttClient = new MqttFactory().CreateMqttClient();

                // 注册事件
                _mqttClient.ConnectedAsync += OnConnectedAsync;
                _mqttClient.DisconnectedAsync += OnDisconnectedAsync;
                _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceivedAsync;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"初始化MQTT客户端失败: {ex.Message}");
            }
        }
        #endregion

        #region 连接管理
        /// <summary>
        /// 连接到MQTT服务器
        /// </summary>
        /// <returns>是否连接成功</returns>
        public async Task<bool> ConnectAsync()
        {
            if (_disposed)
            {
                ErrorOccurred?.Invoke("客户端已释放，无法连接");
                return false;
            }

            try
            {
                if (IsConnected)
                    return true;

                ErrorOccurred?.Invoke($"开始连接到 {ServerEndpoint}:{ServerPort}，客户端ID: {ClientId}");

                var options = new MqttClientOptionsBuilder()
                    .WithTcpServer(ServerEndpoint, ServerPort)
                    .WithCredentials(Username, Password)
                    .WithClientId(ClientId)
                    .WithCleanSession()
                    .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
                    .WithTimeout(TimeSpan.FromSeconds(30))
                    .Build();

                var result = await _mqttClient.ConnectAsync(options);

                if (result.ResultCode == MqttClientConnectResultCode.Success)
                {
                    ErrorOccurred?.Invoke($"连接成功，会话存在: {result.IsSessionPresent}");
                    return true;
                }
                else
                {
                    ErrorOccurred?.Invoke($"连接失败，结果代码: {result.ResultCode}, 原因: {result.ReasonString}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"连接异常: {ex.Message}");
                if (ex.InnerException != null)
                {
                    ErrorOccurred?.Invoke($"内部异常: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            if (_disposed || !IsConnected)
                return;

            try
            {
                await _mqttClient.DisconnectAsync();
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"断开连接失败: {ex.Message}");
            }
        }
        #endregion

        #region 订阅管理
        /// <summary>
        /// 订阅主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="qos">QoS级别</param>
        /// <returns>是否订阅成功</returns>
        public async Task<bool> SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce)
        {
            if (_disposed || string.IsNullOrWhiteSpace(topic))
                return false;

            try
            {
                _subscriptions[topic] = qos;

                if (!IsConnected)
                    return true; // 连接后会自动订阅

                var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                    .WithTopicFilter(topic, qos)
                    .Build();

                var result = await _mqttClient.SubscribeAsync(subscribeOptions);
                return result.Items.Count > 0 &&
                       (result.Items.First().ResultCode == MqttClientSubscribeResultCode.GrantedQoS0 ||
                        result.Items.First().ResultCode == MqttClientSubscribeResultCode.GrantedQoS1 ||
                        result.Items.First().ResultCode == MqttClientSubscribeResultCode.GrantedQoS2);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"订阅主题失败 {topic}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 取消订阅主题
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>是否取消订阅成功</returns>
        public async Task<bool> UnsubscribeAsync(string topic)
        {
            if (_disposed || string.IsNullOrWhiteSpace(topic))
                return false;

            try
            {
                _subscriptions.Remove(topic);

                if (!IsConnected)
                    return true;

                var unsubscribeOptions = new MqttClientUnsubscribeOptionsBuilder()
                    .WithTopicFilter(topic)
                    .Build();

                var result = await _mqttClient.UnsubscribeAsync(unsubscribeOptions);
                return result.Items.Count > 0 && result.Items.First().ResultCode == MqttClientUnsubscribeResultCode.Success;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"取消订阅主题失败 {topic}: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region 消息发送
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="message">消息内容</param>
        /// <param name="qos">QoS级别</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> PublishAsync(string topic, string message, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false)
        {
            if (_disposed || string.IsNullOrWhiteSpace(topic) || !IsConnected)
                return false;

            try
            {
                var mqttMessage = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(Encoding.UTF8.GetBytes(message ?? ""))
                    .WithQualityOfServiceLevel(qos)
                    .WithRetainFlag(retain)
                    .Build();

                var result = await _mqttClient.PublishAsync(mqttMessage);
                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"发送消息失败 {topic}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送字节数组消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息载荷</param>
        /// <param name="qos">QoS级别</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> PublishAsync(string topic, byte[] payload, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false)
        {
            if (_disposed || string.IsNullOrWhiteSpace(topic) || !IsConnected)
                return false;

            try
            {
                var mqttMessage = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(payload ?? new byte[0])
                    .WithQualityOfServiceLevel(qos)
                    .WithRetainFlag(retain)
                    .Build();

                var result = await _mqttClient.PublishAsync(mqttMessage);
                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"发送消息失败 {topic}: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 连接成功事件
        /// </summary>
        private async Task OnConnectedAsync(MqttClientConnectedEventArgs e)
        {
            ConnectionChanged?.Invoke(true);

            // 重新订阅所有主题
            foreach (var subscription in _subscriptions)
            {
                await SubscribeAsync(subscription.Key, subscription.Value);
            }
        }

        /// <summary>
        /// 连接断开事件
        /// </summary>
        private Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs e)
        {
            ConnectionChanged?.Invoke(false);

            // 详细的断开原因分析
            var reason = GetDisconnectReason(e);
            ErrorOccurred?.Invoke($"连接断开: {reason}");

            return Task.CompletedTask;
        }

        /// <summary>
        /// 获取断开连接的详细原因
        /// </summary>
        private string GetDisconnectReason(MqttClientDisconnectedEventArgs e)
        {
            var reasons = new List<string>();

            reasons.Add($"原因: {e.Reason}");

            if (!string.IsNullOrEmpty(e.ReasonString))
            {
                reasons.Add($"详细信息: {e.ReasonString}");
            }

            if (e.Exception != null)
            {
                reasons.Add($"异常: {e.Exception.Message}");
            }

            reasons.Add($"客户端曾连接: {e.ClientWasConnected}");

            // 根据断开原因提供建议
            switch (e.Reason)
            {
                case MqttClientDisconnectReason.NormalDisconnection:
                    reasons.Add("建议: 正常断开，无需处理");
                    break;
                case MqttClientDisconnectReason.NotAuthorized:
                    reasons.Add("建议: 检查用户名和密码");
                    break;
                case MqttClientDisconnectReason.ServerBusy:
                    reasons.Add("建议: 服务器繁忙，稍后重试");
                    break;
                case MqttClientDisconnectReason.BadAuthenticationMethod:
                    reasons.Add("建议: 检查认证方法和凭据");
                    break;
                case MqttClientDisconnectReason.SessionTakenOver:
                    reasons.Add("建议: 客户端ID冲突，已有相同ID的客户端连接");
                    break;
                case MqttClientDisconnectReason.ServerShuttingDown:
                    reasons.Add("建议: 服务器正在关闭，稍后重试");
                    break;
                case MqttClientDisconnectReason.KeepAliveTimeout:
                    reasons.Add("建议: 网络可能不稳定，检查网络连接");
                    break;
                case MqttClientDisconnectReason.AdministrativeAction:
                    reasons.Add("建议: 服务器管理员主动断开连接");
                    break;
                case MqttClientDisconnectReason.ConnectionRateExceeded:
                    reasons.Add("建议: 连接频率过高，降低连接频率");
                    break;
                case MqttClientDisconnectReason.MaximumConnectTime:
                    reasons.Add("建议: 超过最大连接时间，检查服务器配置");
                    break;
                case MqttClientDisconnectReason.UnspecifiedError:
                    reasons.Add("建议: 未指定错误，检查服务器日志");
                    break;
                case MqttClientDisconnectReason.ProtocolError:
                    reasons.Add("建议: 协议错误，检查MQTT版本兼容性");
                    break;
                case MqttClientDisconnectReason.ImplementationSpecificError:
                    reasons.Add("建议: 实现特定错误，联系服务器管理员");
                    break;
                default:
                    reasons.Add("建议: 检查网络连接和服务器状态");
                    break;
            }

            return string.Join("; ", reasons);
        }

        /// <summary>
        /// 消息接收事件
        /// </summary>
        private Task OnMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs e)
        {
            try
            {
                var topic = e.ApplicationMessage.Topic;
                var message = Encoding.UTF8.GetString(e.ApplicationMessage.Payload ?? new byte[0]);
                MessageReceived?.Invoke(topic, message);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke($"处理接收消息异常: {ex.Message}");
            }
            return Task.CompletedTask;
        }
        #endregion

        #region 资源释放
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    if (_mqttClient?.IsConnected == true)
                    {
                        _mqttClient.DisconnectAsync().Wait(5000);
                    }
                    _mqttClient?.Dispose();
                }
                catch { }
                finally
                {
                    _disposed = true;
                }
            }
        }
        #endregion
    }
}
