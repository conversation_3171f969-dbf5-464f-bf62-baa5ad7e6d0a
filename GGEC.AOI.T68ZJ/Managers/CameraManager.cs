using MvCameraControl;
using System.Text;
using System.Diagnostics;
using GGEC.AOI.T68ZJ.Log;

namespace GGEC.AOI.T68ZJ.Managers
{
    public class CameraManager
    {
        List<IDeviceInfo> deviceInfos = new List<IDeviceInfo>();
        List<IDevice> devices = new List<IDevice>();

        // 线程安全的图像缓存 - 用于传感器触发时快速获取
        private readonly object imageLock1 = new object();
        private readonly object imageLock2 = new object();
        private IImage cachedImage1;
        private IImage cachedImage2;
        private DateTime lastCacheTime1 = DateTime.MinValue;
        private DateTime lastCacheTime2 = DateTime.MinValue;

        // 采集线程管理
        private readonly List<Thread> grabThreads = new List<Thread>();
        private readonly List<bool> grabThreadRunning = new List<bool>();
        private readonly object threadLock = new object();

        // 预览线程管理（兼容性）
        private Thread? previewThread;
        private readonly object previewLock = new object();
        private CancellationTokenSource? previewCancellationTokenSource;

        private static CameraManager _instance;

        // 相机参数配置
        public double ExposureTime { get; set; } = 2000.0; // 曝光时间（微秒）
        public double Gain { get; set; } = 20.0; // 增益值
        public int MinPhotoIntervalMs { get; set; } = 200; // 最小拍照间隔（毫秒）
        public string PixelFormat { get; set; } = "Mono8"; // 像素格式

        // 兼容性属性
        public double ConveyorBeltSpeed { get; set; } = 0.3; // 传送带速度（米/秒）
        public int SensorTriggerDelayCompensation { get; set; } = 50; // 传感器触发延迟补偿（毫秒）
        public bool AutoExposureEnabled { get; set; } = false; // 是否启用自动曝光
        public bool AutoGainEnabled { get; set; } = false; // 是否启用自动增益
        public bool ShowLivePreview { get; set; } = false; // 预览状态

        // 拍照质量控制
        private readonly object photoQualityLock = new object();
        private DateTime lastPhotoTime = DateTime.MinValue;

        // 私有构造函数，防止外部实例化
        private CameraManager()
        {
            Logger.Info("CameraManager初始化完成");
        }

        public static CameraManager Instance
        {
            get
            {
                _instance ??= new CameraManager();
                return _instance;
            }
        }

        public void InitCameras()
        {
            Logger.Info("开始初始化相机");

            try
            {
                // 清理之前的设备
                Dispose();

                int nRet = DeviceEnumerator.EnumDevices(DeviceTLayerType.MvGigEDevice, out deviceInfos);
                if (nRet != 0)
                {
                    string errorMsg = GetErrorMessage(nRet);
                    Logger.Error($"枚举设备失败，错误码：{nRet:X8}，错误信息：{errorMsg}");
                    throw new Exception($"枚举设备失败：{errorMsg}");
                }

                if (deviceInfos.Count == 0)
                {
                    Logger.Warn("未找到任何GigE设备");
                    throw new Exception("未找到任何GigE设备，请检查设备连接和网络配置");
                }

                Logger.Info($"找到 {deviceInfos.Count} 个设备");

                for (int i = 0; i < deviceInfos.Count; i++)
                {
                    var deviceInfo = deviceInfos[i];
                    Logger.Info($"正在初始化设备 {i + 1}: {deviceInfo.SerialNumber}");

                    try
                    {
                        IDevice device = DeviceFactory.CreateDevice(deviceInfo);
                        if (device == null)
                        {
                            throw new Exception($"创建设备失败：{deviceInfo.SerialNumber}");
                        }

                        nRet = device.Open();
                        if (nRet != 0)
                        {
                            string errorMsg = GetErrorMessage(nRet);
                            device?.Dispose();
                            throw new Exception($"打开设备失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                        }

                        // 配置设备参数
                        ConfigureDevice(device, deviceInfo);

                        // 开始采集
                        device.StreamGrabber.SetOutputQueueSize(3);
                        device.StreamGrabber.SetImageNodeNum(5);
                        nRet = device.StreamGrabber.StartGrabbing(StreamGrabStrategy.LatestImages);
                        if (nRet != 0)
                        {
                            string errorMsg = GetErrorMessage(nRet);
                            device.Close();
                            device.Dispose();
                            throw new Exception($"开始采集失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                        }

                        devices.Add(device);

                        // 启动图像缓存线程
                        StartImageCacheThread(i);

                        Logger.Info($"设备 {deviceInfo.SerialNumber} 初始化成功");
                    }
                    catch (Exception ex)
                    {
                        Logger.Exception(ex, $"初始化设备 {i + 1} 时发生异常");

                        // 清理已初始化的设备
                        StopAllCacheThreads();
                        foreach (var dev in devices)
                        {
                            try
                            {
                                dev.StreamGrabber.StopGrabbing();
                                dev.Close();
                                dev.Dispose();
                            }
                            catch (Exception cleanupEx)
                            {
                                Logger.Exception(cleanupEx, "清理设备时发生异常");
                            }
                        }
                        devices.Clear();
                        throw;
                    }
                }

                Logger.Info($"相机初始化完成，成功初始化 {devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "相机初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 启动指定设备的图像缓存线程
        /// </summary>
        private void StartImageCacheThread(int deviceIndex)
        {
            lock (threadLock)
            {
                // 确保线程列表大小足够
                while (grabThreads.Count <= deviceIndex)
                {
                    grabThreads.Add(null);
                    grabThreadRunning.Add(false);
                }

                // 如果线程已经在运行，先停止
                if (grabThreads[deviceIndex] != null && grabThreads[deviceIndex].IsAlive)
                {
                    grabThreadRunning[deviceIndex] = false;
                    grabThreads[deviceIndex].Join(1000);
                }

                // 启动新的缓存线程
                grabThreadRunning[deviceIndex] = true;
                grabThreads[deviceIndex] = new Thread(() => ImageCacheWorker(deviceIndex))
                {
                    Name = $"ImageCache_{deviceIndex}",
                    IsBackground = true
                };
                grabThreads[deviceIndex].Start();

                Logger.Info($"设备 {deviceIndex} 的图像缓存线程已启动");
            }
        }

        /// <summary>
        /// 图像缓存线程工作函数 - 持续更新图像缓存，确保传感器触发时有最新图像可用
        /// </summary>
        private void ImageCacheWorker(int deviceIndex)
        {
            Logger.Info($"设备 {deviceIndex} 图像缓存线程开始运行");

            while (grabThreadRunning[deviceIndex])
            {
                try
                {
                    var image = CaptureImageFromDevice(deviceIndex);
                    if (image != null)
                    {
                        UpdateImageCache(deviceIndex, image);
                    }

                    // 控制缓存更新频率，约10fps足够
                    Thread.Sleep(100);
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, $"设备 {deviceIndex} 图像缓存线程发生异常");
                    Thread.Sleep(500);
                }
            }

            Logger.Info($"设备 {deviceIndex} 图像缓存线程已停止");
        }

        /// <summary>
        /// 更新图像缓存 - 保存最新图像供传感器触发时使用
        /// </summary>
        private void UpdateImageCache(int deviceIndex, IImage image)
        {
            if (deviceIndex == 0)
            {
                lock (imageLock1)
                {
                    cachedImage1?.Dispose();
                    cachedImage1 = image;
                    lastCacheTime1 = DateTime.Now;
                }
            }
            else if (deviceIndex == 1)
            {
                lock (imageLock2)
                {
                    cachedImage2?.Dispose();
                    cachedImage2 = image;
                    lastCacheTime2 = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// 停止所有图像缓存线程
        /// </summary>
        private void StopAllCacheThreads()
        {
            lock (threadLock)
            {
                Logger.Info("停止所有图像缓存线程");

                // 设置停止标志
                for (int i = 0; i < grabThreadRunning.Count; i++)
                {
                    grabThreadRunning[i] = false;
                }

                // 等待所有线程结束
                for (int i = 0; i < grabThreads.Count; i++)
                {
                    if (grabThreads[i] != null && grabThreads[i].IsAlive)
                    {
                        try
                        {
                            grabThreads[i].Join(2000);
                            if (grabThreads[i].IsAlive)
                            {
                                Logger.Warn($"缓存线程 {i} 未能在2秒内停止");
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Exception(ex, $"停止缓存线程 {i} 时发生异常");
                        }
                    }
                }

                grabThreads.Clear();
                grabThreadRunning.Clear();
            }
        }

        /// <summary>
        /// 配置设备参数 - 针对传送带AOI检测优化
        /// </summary>
        private void ConfigureDevice(IDevice device, IDeviceInfo deviceInfo)
        {
            try
            {
                Logger.Info($"开始配置设备参数：{deviceInfo.SerialNumber}");

                // 如果是GigE设备，优化网络参数
                if (device is IGigEDevice gigEDevice)
                {
                    Logger.Info($"配置GigE设备网络参数：{deviceInfo.SerialNumber}");

                    // 获取最佳包大小
                    int optimalPacketSize;
                    int result = gigEDevice.GetOptimalPacketSize(out optimalPacketSize);
                    if (result == 0)
                    {
                        result = device.Parameters.SetIntValue("GevSCPSPacketSize", optimalPacketSize);
                        if (result == 0)
                        {
                            Logger.Info($"设置包大小为：{optimalPacketSize}");
                        }
                        else
                        {
                            Logger.Warn($"设置包大小失败，错误码：{result:X8}");
                        }
                    }
                    else
                    {
                        Logger.Warn($"获取最佳包大小失败，错误码：{result:X8}");
                    }
                }

                // 设置图像格式和尺寸
                ConfigureImageFormat(device);

                // 设置采集模式
                ConfigureAcquisitionMode(device);

                // 设置曝光和增益参数（针对传送带优化）
                ConfigureExposureAndGain(device);

                // 设置触发模式
                ConfigureTriggerMode(device);

                Logger.Info($"设备参数配置完成：{deviceInfo.SerialNumber}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"配置设备参数时发生异常：{deviceInfo.SerialNumber}");
                // 配置失败不抛出异常，使用默认参数
            }
        }

        /// <summary>
        /// 配置图像格式和尺寸
        /// </summary>
        private void ConfigureImageFormat(IDevice device)
        {
            try
            {
                // 设置像素格式（使用变量中的设置）
                var pixelFormatResult = device.Parameters.SetEnumValueByString("PixelFormat", PixelFormat);
                if (pixelFormatResult == 0)
                {
                    Logger.Info($"设置像素格式为{PixelFormat}");
                }
                else
                {
                    Logger.Warn($"设置像素格式失败，错误码：{pixelFormatResult:X8}，尝试使用默认格式");
                    // 如果设置失败，尝试常见的格式
                    var fallbackFormats = new[] { "Mono8", "RGB8", "BGR8", "BayerRG8" };
                    foreach (var format in fallbackFormats)
                    {
                        var fallbackResult = device.Parameters.SetEnumValueByString("PixelFormat", format);
                        if (fallbackResult == 0)
                        {
                            Logger.Info($"使用备用像素格式：{format}");
                            PixelFormat = format; // 更新变量
                            break;
                        }
                    }
                }

                // 获取并记录当前图像尺寸
                device.Parameters.GetIntValue("Width", out IIntValue width);
                device.Parameters.GetIntValue("Height", out IIntValue height);
                Logger.Info($"当前图像尺寸: {width?.CurValue} x {height?.CurValue}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置图像格式时发生异常");
            }
        }

        /// <summary>
        /// 配置采集模式
        /// </summary>
        private void ConfigureAcquisitionMode(IDevice device)
        {
            try
            {
                // 设置连续采集模式
                var result = device.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
                if (result == 0)
                {
                    Logger.Info("设置为连续采集模式");
                }
                else
                {
                    Logger.Warn($"设置连续采集模式失败，错误码：{result:X8}");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置采集模式时发生异常");
            }
        }

        /// <summary>
        /// 配置曝光时间和增益 - 使用变量参数
        /// </summary>
        private void ConfigureExposureAndGain(IDevice device)
        {
            try
            {
                // 设置自动曝光模式
                if (AutoExposureEnabled)
                {
                    var autoExposureResult = device.Parameters.SetEnumValueByString("ExposureAuto", "Continuous");
                    if (autoExposureResult == 0)
                    {
                        Logger.Info("启用自动曝光模式");
                    }
                    else
                    {
                        Logger.Warn($"启用自动曝光失败，错误码：{autoExposureResult:X8}");
                    }
                }
                else
                {
                    // 关闭自动曝光，使用手动设置
                    device.Parameters.SetEnumValueByString("ExposureAuto", "Off");

                    // 设置曝光时间
                    var exposureResult = device.Parameters.SetFloatValue("ExposureTime", (float)ExposureTime);
                    if (exposureResult == 0)
                    {
                        Logger.Info($"设置曝光时间为{ExposureTime}微秒（适合传送带运动）");
                    }
                    else
                    {
                        Logger.Warn($"设置曝光时间失败，错误码：{exposureResult:X8}");
                    }
                }

                // 设置自动增益模式
                if (AutoGainEnabled)
                {
                    var autoGainResult = device.Parameters.SetEnumValueByString("GainAuto", "Continuous");
                    if (autoGainResult == 0)
                    {
                        Logger.Info("启用自动增益模式");
                    }
                    else
                    {
                        Logger.Warn($"启用自动增益失败，错误码：{autoGainResult:X8}");
                    }
                }
                else
                {
                    // 关闭自动增益，使用手动设置
                    device.Parameters.SetEnumValueByString("GainAuto", "Off");

                    // 设置增益值
                    var gainResult = device.Parameters.SetFloatValue("Gain", (float)Gain);
                    if (gainResult == 0)
                    {
                        Logger.Info($"设置增益为{Gain}");
                    }
                    else
                    {
                        Logger.Warn($"设置增益失败，错误码：{gainResult:X8}");
                    }
                }

                // 记录当前曝光和增益设置
                device.Parameters.GetFloatValue("ExposureTime", out IFloatValue exposureTime);
                device.Parameters.GetFloatValue("Gain", out IFloatValue gain);
                Logger.Info($"当前曝光时间: {exposureTime?.CurValue}μs, 增益: {gain?.CurValue}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置曝光和增益时发生异常");
            }
        }

        /// <summary>
        /// 配置触发模式
        /// </summary>
        private void ConfigureTriggerMode(IDevice device)
        {
            try
            {
                // 设置触发模式为关闭（连续采集）
                var result = device.Parameters.SetEnumValueByString("TriggerMode", "Off");
                if (result == 0)
                {
                    Logger.Info("关闭触发模式（连续采集）");
                }
                else
                {
                    Logger.Warn($"关闭触发模式失败，错误码：{result:X8}");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置触发模式时发生异常");
            }
        }













        /// <summary>
        /// 安全地更新PictureBox图像（处理UI线程调用）
        /// </summary>
        private void UpdatePictureBoxSafely(PictureBox pictureBox, IImage image)
        {
            if (pictureBox == null || image == null) return;

            try
            {
                var bitmap = image.ToBitmap();
                if (bitmap == null) return;

                if (pictureBox.InvokeRequired)
                {
                    pictureBox.Invoke(new Action(() =>
                    {
                        var oldImage = pictureBox.Image;
                        pictureBox.Image = bitmap;
                        oldImage?.Dispose();
                    }));
                }
                else
                {
                    var oldImage = pictureBox.Image;
                    pictureBox.Image = bitmap;
                    oldImage?.Dispose();
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "更新PictureBox图像时发生异常");
            }
        }

        /// <summary>
        /// 传感器触发的拍照 - 直接使用预缓存的图像，确保位置一致性
        /// </summary>
        public (IImage? image1, IImage? image2) TakeSensorTriggeredPhotos()
        {
            lock (photoQualityLock)
            {
                // 防止频繁触发
                var timeSinceLastPhoto = DateTime.Now - lastPhotoTime;
                if (timeSinceLastPhoto.TotalMilliseconds < MinPhotoIntervalMs)
                {
                    Logger.Warn($"拍照间隔过短（{timeSinceLastPhoto.TotalMilliseconds:F0}ms），忽略此次触发");
                    return (null, null);
                }
                lastPhotoTime = DateTime.Now;
            }

            if (devices.Count < 2)
            {
                Logger.Error($"设备数量不足，当前设备数：{devices.Count}，需要至少2个设备");
                return (null, null);
            }

            Logger.Info("传感器触发拍照 - 使用预缓存图像");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                IImage? image1 = null;
                IImage? image2 = null;

                // 获取预缓存的图像
                lock (imageLock1)
                {
                    if (cachedImage1 != null)
                    {
                        // 检查图像是否太旧（超过1秒）
                        var imageAge = DateTime.Now - lastCacheTime1;
                        if (imageAge.TotalMilliseconds < 1000)
                        {
                            image1 = cachedImage1;
                            Logger.Debug($"使用相机1预缓存图像，图像年龄：{imageAge.TotalMilliseconds:F0}ms");
                        }
                        else
                        {
                            Logger.Warn($"相机1缓存图像过旧（{imageAge.TotalMilliseconds:F0}ms），跳过");
                        }
                    }
                    else
                    {
                        Logger.Warn("相机1无可用缓存图像");
                    }
                }

                lock (imageLock2)
                {
                    if (cachedImage2 != null)
                    {
                        // 检查图像是否太旧（超过1秒）
                        var imageAge = DateTime.Now - lastCacheTime2;
                        if (imageAge.TotalMilliseconds < 1000)
                        {
                            image2 = cachedImage2;
                            Logger.Debug($"使用相机2预缓存图像，图像年龄：{imageAge.TotalMilliseconds:F0}ms");
                        }
                        else
                        {
                            Logger.Warn($"相机2缓存图像过旧（{imageAge.TotalMilliseconds:F0}ms），跳过");
                        }
                    }
                    else
                    {
                        Logger.Warn("相机2无可用缓存图像");
                    }
                }

                stopwatch.Stop();
                Logger.Info($"传感器触发拍照完成，耗时：{stopwatch.ElapsedMilliseconds}ms，" +
                           $"相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}");

                return (image1, image2);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Logger.Exception(ex, $"传感器触发拍照异常，耗时：{stopwatch.ElapsedMilliseconds}ms");
                return (null, null);
            }
        }







        /// <summary>
        /// 从相机硬件获取图像 - 用于图像缓存线程
        /// </summary>
        private IImage? CaptureImageFromDevice(int deviceIndex)
        {
            if (deviceIndex >= devices.Count)
            {
                return null;
            }

            try
            {
                var device = devices[deviceIndex];
                int nRet = device.StreamGrabber.GetImageBuffer(1000, out IFrameOut frameOut);

                if (nRet == 0 && frameOut?.Image != null)
                {
                    var image = frameOut.Image;
                    frameOut.Dispose();
                    return image;
                }
                else
                {
                    frameOut?.Dispose();
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"设备 {deviceIndex} 获取图像时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 兼容性方法 - 单个相机拍照
        /// </summary>
        public IImage? TakePhoto(int deviceIndex)
        {
            if (deviceIndex == 0)
            {
                lock (imageLock1)
                {
                    return cachedImage1;
                }
            }
            else if (deviceIndex == 1)
            {
                lock (imageLock2)
                {
                    return cachedImage2;
                }
            }
            return null;
        }

        /// <summary>
        /// 兼容性方法 - 异步传感器触发拍照
        /// </summary>
        public async Task<(IImage? image1, IImage? image2)> TakeSensorTriggeredPhotosAsync()
        {
            return await Task.Run(() => TakeSensorTriggeredPhotos());
        }

        /// <summary>
        /// 兼容性方法 - 自动调整传送带参数
        /// </summary>
        public void AutoAdjustForConveyorSpeed()
        {
            Logger.Info($"根据传送带速度 {ConveyorBeltSpeed}m/s 自动调整参数");
            // 简化实现，不做实际调整
        }

        /// <summary>
        /// 兼容性方法 - 记录相机参数
        /// </summary>
        public void LogCurrentCameraParameters(int deviceIndex)
        {
            Logger.Info($"设备 {deviceIndex} 当前参数：曝光时间={ExposureTime}μs, 增益={Gain}");
        }

        /// <summary>
        /// 兼容性方法 - 获取内存使用情况
        /// </summary>
        public string GetMemoryUsage()
        {
            long memory = GC.GetTotalMemory(false) / 1024 / 1024;
            return $"当前内存: {memory} MB";
        }

        /// <summary>
        /// 兼容性方法 - 开始预览
        /// </summary>
        public void StartLivePreview(object pictureBox1, object pictureBox2)
        {
            ShowLivePreview = true;
            Logger.Info("预览功能已简化，仅设置状态标志");
        }

        /// <summary>
        /// 兼容性方法 - 停止预览
        /// </summary>
        public void StopLivePreview()
        {
            ShowLivePreview = false;
            Logger.Info("停止预览");
        }



        /// <summary>
        /// 根据错误码获取详细错误信息
        /// </summary>
        private string GetErrorMessage(int errorCode)
        {
            switch (errorCode)
            {
                case MvError.MV_E_HANDLE: return "错误或无效句柄";
                case MvError.MV_E_SUPPORT: return "不支持的功能";
                case MvError.MV_E_BUFOVER: return "缓存已满";
                case MvError.MV_E_CALLORDER: return "函数调用顺序错误";
                case MvError.MV_E_PARAMETER: return "参数错误";
                case MvError.MV_E_RESOURCE: return "申请资源失败";
                case MvError.MV_E_NODATA: return "无数据（超时）";
                case MvError.MV_E_PRECONDITION: return "前置条件错误或运行环境改变";
                case MvError.MV_E_VERSION: return "版本不匹配";
                case MvError.MV_E_NOENOUGH_BUF: return "内存不足";
                case MvError.MV_E_UNKNOW: return "未知错误";
                case MvError.MV_E_GC_GENERIC: return "通用错误";
                case MvError.MV_E_GC_ACCESS: return "节点访问条件错误";
                case MvError.MV_E_ACCESS_DENIED: return "无权限";
                case MvError.MV_E_BUSY: return "设备忙碌或网络断开";
                case MvError.MV_E_NETER: return "网络错误";
                case unchecked((int)0x8000000D): return "没有可输出的缓存";
                case unchecked((int)0x8000000E): return "异常图像或数据格式错误";
                case unchecked((int)0x8000000F): return "图像数据不完整";
                case unchecked((int)0x80000010): return "设备断开连接";
                case unchecked((int)0x80000011): return "超时错误";
                case unchecked((int)0x80000012): return "内存分配失败";
                case unchecked((int)0x80000013): return "设备初始化失败";
                default: return $"未知错误码：{errorCode:X8}";
            }
        }

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        private bool ShouldRetry(int errorCode)
        {
            switch (errorCode)
            {
                case MvError.MV_E_NODATA: // 无数据（超时）
                case MvError.MV_E_BUFOVER: // 缓存已满
                case MvError.MV_E_NETER: // 网络错误
                case MvError.MV_E_BUSY: // 设备忙碌
                case unchecked((int)0x8000000D): // 没有可输出的缓存
                case unchecked((int)0x8000000E): // 异常图像或数据格式错误
                case unchecked((int)0x8000000F): // 图像数据不完整
                case unchecked((int)0x80000011): // 超时错误
                    return true;
                case MvError.MV_E_HANDLE: // 错误或无效句柄
                case MvError.MV_E_SUPPORT: // 不支持的功能
                case MvError.MV_E_PARAMETER: // 参数错误
                case MvError.MV_E_ACCESS_DENIED: // 无权限
                case unchecked((int)0x80000010): // 设备断开连接
                case unchecked((int)0x80000013): // 设备初始化失败
                    return false;
                default:
                    return true; // 默认重试
            }
        }

        public void Dispose()
        {
            Logger.Info("开始释放相机资源");

            try
            {
                // 停止所有缓存线程
                StopAllCacheThreads();

                // 释放设备资源
                foreach (var device in devices)
                {
                    try
                    {
                        device?.StreamGrabber.StopGrabbing();
                        device?.Close();
                        device?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Logger.Exception(ex, "释放设备时发生异常");
                    }
                }

                devices.Clear();
                deviceInfos.Clear();

                // 释放缓存图像
                try
                {
                    cachedImage1?.Dispose();
                    cachedImage1 = null;
                    cachedImage2?.Dispose();
                    cachedImage2 = null;
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, "释放缓存图像时发生异常");
                }

                Logger.Info("相机资源释放完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "释放相机资源时发生异常");
            }
        }

        /// <summary>
        /// 获取设备状态信息
        /// </summary>
        public string GetDeviceStatus()
        {
            var status = new StringBuilder();
            status.AppendLine($"设备总数: {devices.Count}");
            status.AppendLine($"设备信息总数: {deviceInfos.Count}");
            status.AppendLine($"实时预览状态: {ShowLivePreview}");

            for (int i = 0; i < devices.Count; i++)
            {
                try
                {
                    var device = devices[i];
                    var deviceInfo = i < deviceInfos.Count ? deviceInfos[i] : null;

                    status.AppendLine($"设备 {i + 1}:");
                    status.AppendLine($"  序列号: {deviceInfo?.SerialNumber ?? "未知"}");
                    status.AppendLine($"  用户定义名称: {deviceInfo?.UserDefinedName ?? "未知"}");
                    status.AppendLine($"  设备对象: {(device != null ? "已创建" : "未创建")}");

                    if (device != null)
                    {
                        // 检查设备是否仍然连接
                        try
                        {
                            // 尝试读取一个简单的参数来检查连接状态
                            var result = device.Parameters.GetIntValue("Width", out IIntValue widthValue);
                            status.AppendLine($"  连接状态: {(result == 0 ? "已连接" : $"连接异常(错误码:{result:X8})")}");
                            if (result == 0 && widthValue != null)
                            {
                                status.AppendLine($"  图像宽度: {widthValue.ToString()}");
                            }
                        }
                        catch (Exception ex)
                        {
                            status.AppendLine($"  连接状态: 异常 - {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    status.AppendLine($"设备 {i + 1}: 获取状态时发生异常 - {ex.Message}");
                }
            }

            return status.ToString();
        }

        /// <summary>
        /// 重新连接设备
        /// </summary>
        public bool ReconnectDevice(int deviceIndex)
        {
            if (deviceIndex >= devices.Count || deviceIndex >= deviceInfos.Count)
            {
                Logger.Error($"设备索引超出范围：{deviceIndex}");
                return false;
            }

            try
            {
                Logger.Info($"尝试重新连接设备 {deviceIndex}");

                var deviceInfo = deviceInfos[deviceIndex];
                var oldDevice = devices[deviceIndex];

                // 关闭旧设备
                try
                {
                    oldDevice?.StreamGrabber.StopGrabbing();
                    oldDevice?.Close();
                    oldDevice?.Dispose();
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, $"关闭旧设备 {deviceIndex} 时发生异常");
                }

                // 创建新设备
                IDevice newDevice = DeviceFactory.CreateDevice(deviceInfo);
                if (newDevice == null)
                {
                    Logger.Error($"重新创建设备失败：{deviceInfo.SerialNumber}");
                    return false;
                }

                // 打开设备
                int nRet = newDevice.Open();
                if (nRet != 0)
                {
                    string errorMsg = GetErrorMessage(nRet);
                    Logger.Error($"重新打开设备失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                    newDevice.Dispose();
                    return false;
                }

                // 配置设备
                ConfigureDevice(newDevice, deviceInfo);

                // 开始采集
                newDevice.StreamGrabber.SetOutputQueueSize(3);
                newDevice.StreamGrabber.SetImageNodeNum(5);
                nRet = newDevice.StreamGrabber.StartGrabbing(StreamGrabStrategy.LatestImages);
                if (nRet != 0)
                {
                    string errorMsg = GetErrorMessage(nRet);
                    Logger.Error($"重新开始采集失败：{deviceInfo.SerialNumber}，错误：{errorMsg}");
                    newDevice.Close();
                    newDevice.Dispose();
                    return false;
                }

                // 替换设备
                devices[deviceIndex] = newDevice;
                Logger.Info($"设备 {deviceIndex} 重新连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"重新连接设备 {deviceIndex} 时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 检查所有设备的健康状态
        /// </summary>
        public void CheckDeviceHealth()
        {
            Logger.Info("开始检查设备健康状态");

            for (int i = 0; i < devices.Count; i++)
            {
                try
                {
                    var device = devices[i];
                    if (device == null)
                    {
                        Logger.Warn($"设备 {i} 对象为空");
                        continue;
                    }

                    // 尝试获取设备参数来检查连接状态
                    var result = device.Parameters.GetIntValue("Width", out IIntValue widthValue);
                    if (result != 0)
                    {
                        Logger.Warn($"设备 {i} 连接异常，错误码：{result:X8}，尝试重新连接");
                        ReconnectDevice(i);
                    }
                    else
                    {
                        Logger.Debug($"设备 {i} 状态正常");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Exception(ex, $"检查设备 {i} 健康状态时发生异常");
                }
            }
        }


    }
}
